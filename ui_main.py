#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据抓取器 - 重构后的主UI界面
基于模块化设计的现代化图形界面
"""

import sys
import os
from datetime import datetime
from typing import Dict, List

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QSplitter, QMessageBox
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QWaitCondition, QMutex
)
from PyQt6.QtGui import QFont

# 导入重构后的模块
from ui_panels import (
    ConfigPanel, ControlPanel, ProgressPanel, LogPanel, ResultPanel
)
from ui_components import (
    CaptchaDialog, get_modern_window_style
)
from douyin import DouyinScraper
from utils import (
    load_config, is_batch_mode_enabled, get_douyin_ids_from_config,
    BatchProcessingReport, check_privacy_status, generate_batch_filename,
    get_data_file_path, json_to_csv
)
from logger import setup_logger
import main  # 导入批量处理逻辑


class BatchScraperWorker(QThread):
    """批量数据抓取工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)      # 整体进度更新
    status_updated = pyqtSignal(str)        # 状态更新  
    log_message = pyqtSignal(str, str)      # 日志消息 (message, level)
    batch_progress = pyqtSignal(int, int)   # 批量进度 (current, total)
    current_id_updated = pyqtSignal(str)    # 当前处理ID
    data_received = pyqtSignal(dict)        # 数据接收
    error_occurred = pyqtSignal(str)        # 错误发生
    finished = pyqtSignal()                 # 完成信号
    captcha_required = pyqtSignal(str)      # 滑块验证需求信号
    captcha_completed = pyqtSignal()        # 验证完成响应信号
    
    def __init__(self, config: Dict, scrape_types: List[str]):
        super().__init__()
        self.config = config
        self.scrape_types = scrape_types
        self.is_running = True
        self.batch_report = BatchProcessingReport()
        
        # 滑块验证等待机制
        self.captcha_wait_condition = QWaitCondition()
        self.captcha_mutex = QMutex()
        self.captcha_user_cancelled = False
        
    def wait_for_captcha_completion(self, scene: str) -> bool:
        """等待用户完成滑块验证"""
        self.log_message.emit(f"检测到滑块验证需求 - 场景: {scene}", "WARNING")
        self.status_updated.emit(f"等待滑块验证: {scene}")
        
        # 发送验证需求信号
        self.captcha_required.emit(scene)
        
        # 等待用户操作
        self.captcha_mutex.lock()
        try:
            # 最多等待5分钟
            timeout_ms = 5 * 60 * 1000
            if not self.captcha_wait_condition.wait(self.captcha_mutex, timeout_ms):
                self.log_message.emit("滑块验证等待超时", "ERROR")
                return False
            
            if self.captcha_user_cancelled:
                self.log_message.emit("用户取消了滑块验证", "WARNING")
                return False
            
            self.log_message.emit("用户完成滑块验证，继续执行", "SUCCESS")
            return True
            
        finally:
            self.captcha_mutex.unlock()
    
    def on_captcha_completed(self, user_cancelled: bool = False):
        """接收验证完成信号"""
        self.captcha_mutex.lock()
        try:
            self.captcha_user_cancelled = user_cancelled
            self.captcha_wait_condition.wakeAll()
        finally:
            self.captcha_mutex.unlock()
        
    def run(self):
        """执行批量抓取任务"""
        try:
            # 检查是否为批量模式
            if not is_batch_mode_enabled(self.config):
                self.run_single_mode()
                return
                
            # 批量模式
            self.run_batch_mode()
            
        except Exception as e:
            self.error_occurred.emit(f"批量抓取失败: {str(e)}")
        finally:
            self.finished.emit()
    
    def run_single_mode(self):
        """单个ID模式运行"""
        try:
            self.status_updated.emit("初始化抓取器...")
            scraper = DouyinScraper(ui_callback=self.wait_for_captcha_completion)
            
            # 使用utils函数获取ID列表，然后取第一个
            douyin_ids = get_douyin_ids_from_config(self.config)
            if not douyin_ids:
                self.error_occurred.emit("请输入抖音ID")
                return
            
            douyin_id = douyin_ids[0]  # 单个模式下取第一个ID
            self.current_id_updated.emit(douyin_id)
            
            # 获取sec_uid
            self.status_updated.emit("获取用户ID...")
            sec_uid = scraper.fetch_sec_uid(douyin_id)
            
            # 执行抓取任务
            self.execute_scrape_tasks(scraper, sec_uid, douyin_id)
            
        except Exception as e:
            self.error_occurred.emit(f"单个模式抓取失败: {str(e)}")
        finally:
            try:
                scraper.close()
            except:
                pass
    
    def run_batch_mode(self):
        """批量模式运行"""
        try:
            douyin_ids = get_douyin_ids_from_config(self.config)
            if not douyin_ids:
                self.error_occurred.emit("批量模式下没有找到有效的抖音ID")
                return
            
            self.batch_progress.emit(0, len(douyin_ids))
            self.log_message.emit(f"开始批量处理 {len(douyin_ids)} 个抖音ID", "INFO")
            
            # 使用main模块的批量处理功能
            success_count = 0
            for i, douyin_id in enumerate(douyin_ids, 1):
                if not self.is_running:
                    break
                    
                self.current_id_updated.emit(douyin_id)
                self.batch_progress.emit(i, len(douyin_ids))
                self.status_updated.emit(f"处理ID {i}/{len(douyin_ids)}: {douyin_id}")
                
                try:
                    # 创建新的抓取器实例
                    scraper = DouyinScraper(ui_callback=self.wait_for_captcha_completion)
                    
                    # 获取sec_uid
                    sec_uid = scraper.fetch_sec_uid(douyin_id)
                    
                    # 执行抓取任务
                    self.execute_scrape_tasks(scraper, sec_uid, douyin_id)
                    success_count += 1
                    
                except Exception as e:
                    self.log_message.emit(f"处理ID {douyin_id} 失败: {str(e)}", "ERROR")
                finally:
                    try:
                        scraper.close()
                    except:
                        pass
                
                # 批量间隔
                if i < len(douyin_ids):
                    import time
                    interval = self.config.get('batch', {}).get('batch_interval_seconds', 5)
                    time.sleep(interval)
            
            self.log_message.emit(f"批量处理完成 - 成功: {success_count}/{len(douyin_ids)}", "SUCCESS")
            
        except Exception as e:
            self.error_occurred.emit(f"批量处理失败: {str(e)}")
    
    def execute_scrape_tasks(self, scraper, sec_uid, douyin_id):
        """执行具体的抓取任务"""
        stats = {'user_count': 0, 'follower_count': 0, 'video_count': 0}

        # 初始化当前用户数据存储
        self.current_user_data = {}

        # 第一步：始终获取用户资料
        self.status_updated.emit("获取用户信息...")
        user_data = scraper.fetch_user_profile(sec_uid)
        stats['user_count'] = 1

        # 检查隐私状态
        privacy_status = check_privacy_status(user_data)

        # 保存用户数据
        self.current_user_data['user_profile'] = user_data

        self.data_received.emit({
            'type': 'user_profile',
            'data': user_data
        })
        self.log_message.emit("用户信息获取完成", "SUCCESS")

        # 第二步：根据隐私状态和用户选择的任务类型决定执行什么

        # 处理私密账号情况
        if privacy_status['is_private_account']:
            self.log_message.emit(f"用户 {douyin_id} 为私密账号: {privacy_status['private_reason']}", "INFO")
            self.log_message.emit("跳过粉丝列表和喜欢列表获取", "INFO")

            # 为用户选择的任务返回空数据
            if "followers" in self.scrape_types:
                self.data_received.emit({'type': 'followers', 'data': []})
            if "favorites" in self.scrape_types:
                self.data_received.emit({'type': 'favorites', 'data': []})
            return  # 私密账号直接返回

        # 处理非私密账号的各项任务
        for task_type in self.scrape_types:
            if not self.is_running:
                break

            if task_type == "user_profile":
                # 用户资料已经获取完成，跳过
                continue

            elif task_type == "followers":
                if not privacy_status['followers_accessible']:
                    self.log_message.emit(f"用户 {douyin_id} 粉丝列表不可访问", "INFO")
                    self.data_received.emit({'type': 'followers', 'data': []})
                else:
                    self.status_updated.emit("获取粉丝列表...")
                    max_followers = self.config.get('limits', {}).get('max_follower_count', 30)
                    followers_data = scraper.fetch_followers(sec_uid, max_followers)
                    stats['follower_count'] = len(followers_data)

                    # 保存粉丝数据
                    self.current_user_data['followers'] = followers_data

                    self.data_received.emit({
                        'type': 'followers',
                        'data': followers_data
                    })
                    self.log_message.emit(f"粉丝列表获取完成 - 数量: {len(followers_data)}", "SUCCESS")

            elif task_type == "favorites":
                if not privacy_status['favorites_visible']:
                    self.log_message.emit(f"用户 {douyin_id} 喜欢列表不可见: {privacy_status['favorites_reason']}", "INFO")
                    self.data_received.emit({'type': 'favorites', 'data': []})
                else:
                    self.status_updated.emit("获取喜欢列表...")
                    max_favorites = self.config.get('limits', {}).get('max_favorite_count', 20)
                    favorites_data = scraper.fetch_favorites(sec_uid, max_favorites, privacy_check=privacy_status)
                    stats['video_count'] = len(favorites_data)

                    # 保存喜欢列表数据
                    self.current_user_data['favorites'] = favorites_data

                    self.data_received.emit({
                        'type': 'favorites',
                        'data': favorites_data
                    })
                    self.log_message.emit(f"喜欢列表获取完成 - 数量: {len(favorites_data)}", "SUCCESS")

        # 自动保存用户数据
        self.auto_save_user_data(douyin_id, privacy_status)

    def auto_save_user_data(self, douyin_id: str, privacy_status: dict):
        """自动保存用户数据到文件"""
        try:
            import os
            import json
            from datetime import datetime

            # 创建data目录
            data_dir = "data"
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)

            # 生成时间戳
            timestamp = int(datetime.now().timestamp())

            # 获取当前用户的所有数据
            user_data = {}
            if hasattr(self, 'current_user_data'):
                user_data = self.current_user_data

            # 保存用户基本信息
            if 'user_profile' in user_data and user_data['user_profile']:
                json_filename = get_data_file_path(
                    generate_batch_filename(douyin_id, "user_profile", "id_timestamp") + ".json"
                )
                with open(json_filename, 'w', encoding='utf-8') as f:
                    json.dump(user_data['user_profile'], f, ensure_ascii=False, indent=2)
                self.log_message.emit(f"用户信息已保存: {os.path.basename(json_filename)}", "SUCCESS")

            # 保存粉丝列表（如果有数据）
            if 'followers' in user_data and user_data['followers']:
                json_filename = get_data_file_path(
                    generate_batch_filename(douyin_id, "followers_details", "id_timestamp") + ".json"
                )
                with open(json_filename, 'w', encoding='utf-8') as f:
                    json.dump(user_data['followers'], f, ensure_ascii=False, indent=2)

                csv_filename = get_data_file_path(
                    generate_batch_filename(douyin_id, "followers_details", "id_timestamp") + ".csv"
                )
                json_to_csv(user_data['followers'], csv_filename)
                self.log_message.emit(f"粉丝列表已保存: {os.path.basename(json_filename)}", "SUCCESS")

            # 保存喜欢列表（如果有数据）
            if 'favorites' in user_data and user_data['favorites']:
                json_filename = get_data_file_path(
                    generate_batch_filename(douyin_id, "favorites_details", "id_timestamp") + ".json"
                )
                with open(json_filename, 'w', encoding='utf-8') as f:
                    json.dump(user_data['favorites'], f, ensure_ascii=False, indent=2)

                csv_filename = get_data_file_path(
                    generate_batch_filename(douyin_id, "favorites_details", "id_timestamp") + ".csv"
                )
                json_to_csv(user_data['favorites'], csv_filename)
                self.log_message.emit(f"喜欢列表已保存: {os.path.basename(json_filename)}", "SUCCESS")

        except Exception as e:
            self.log_message.emit(f"自动保存失败: {str(e)}", "ERROR")

    def stop(self):
        """停止抓取"""
        self.is_running = False
        self.log_message.emit("用户停止了抓取任务", "WARNING")


class DouyinScraperMainWindow(QMainWindow):
    """抖音数据抓取器主窗口"""
    
    def __init__(self):
        super().__init__()
        self.scraper_worker = None
        self.start_time = None
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化UI - 优化三列布局"""
        self.setWindowTitle("抖音数据抓取器 v2.0 - 重构版")
        self.setMinimumSize(1400, 900)  # 增加最小宽度以适应三列布局
        self.resize(1600, 1000)  # 增加默认尺寸
        
        # 应用现代化样式
        self.apply_modern_style()
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 - 三列水平分割
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建主分割器 - 三列布局
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # === 左列：配置面板 ===
        self.config_panel = ConfigPanel()
        main_splitter.addWidget(self.config_panel)
        
        # === 中列：控制+进度+日志面板 ===
        middle_panel = QWidget()
        middle_layout = QVBoxLayout(middle_panel)
        middle_layout.setContentsMargins(0, 0, 0, 0)
        middle_layout.setSpacing(0)
        
        # 控制面板
        self.control_panel = ControlPanel()
        middle_layout.addWidget(self.control_panel, 0)  # 固定大小
        
        # 进度面板
        self.progress_panel = ProgressPanel()
        self.progress_panel.setMinimumWidth(350)  # 增加最小宽度
        self.progress_panel.setMaximumWidth(450)  # 适当增加最大宽度
        middle_layout.addWidget(self.progress_panel, 0)  # 固定大小
        
        # 日志面板
        self.log_panel = LogPanel()
        middle_layout.addWidget(self.log_panel, 1)  # 可伸缩
        
        main_splitter.addWidget(middle_panel)
        
        # === 右列：结果面板 ===
        self.result_panel = ResultPanel()
        main_splitter.addWidget(self.result_panel)
        
        # 设置三列的初始比例：配置面板:中间面板:结果面板 = 5:3:4
        # 对于1600px宽度：约667px:400px:533px
        main_splitter.setSizes([667, 400, 533])
        
        # 设置分割器属性
        main_splitter.setChildrenCollapsible(False)  # 防止面板被完全折叠
        
        main_layout.addWidget(main_splitter)
    
    def apply_modern_style(self):
        """应用现代化样式"""
        self.setStyleSheet(get_modern_window_style())
    
    def setup_connections(self):
        """设置信号连接"""
        # 控制面板信号
        self.control_panel.start_scraping.connect(self.start_scraping)
        self.control_panel.stop_scraping.connect(self.stop_scraping)
    
    def start_scraping(self, scrape_types: List[str]):
        """开始抓取"""
        try:
            # 获取配置
            config = self.config_panel.get_config_dict()
            
            # 检查配置
            if not config.get('douyin_ids'):
                QMessageBox.warning(self, "警告", "请输入抖音ID！")
                self.control_panel.reset_buttons()
                return
            
            # 构建完整的配置结构，确保与utils.py函数兼容
            full_config = {
                'douyin_id': {
                    'douyin_ids': config['douyin_ids'],
                    'enable_batch_mode': config['enable_batch_mode']
                },
                'scraper': {
                    'js_timeout': config['js_timeout'],
                    'js_retry': config['js_retry']
                },
                'limits': {
                    'max_follower_count': config['max_follower_count'],
                    'max_favorite_count': config['max_favorite_count']
                },
                'optimization': {
                    'optimization_stage': config['optimization_stage']
                },
                'concurrent': {
                    'enable_concurrent_mode': config['enable_concurrent_mode'],
                    'max_concurrent_tabs': config['max_concurrent_tabs']
                },
                'batch': {
                    'batch_interval_seconds': config['batch_interval_seconds'],
                    'id_retry_count': config['id_retry_count'],
                    'skip_failed_ids': config['skip_failed_ids'],
                    'generate_batch_report': config['generate_batch_report']
                }
            }
            
            # 创建工作线程
            self.scraper_worker = BatchScraperWorker(full_config, scrape_types)
            
            # 连接信号
            self.scraper_worker.progress_updated.connect(self.progress_panel.update_progress)
            self.scraper_worker.status_updated.connect(self.progress_panel.update_status)
            self.scraper_worker.log_message.connect(self.log_panel.add_log)
            self.scraper_worker.batch_progress.connect(self.progress_panel.update_batch_progress)
            self.scraper_worker.current_id_updated.connect(self.progress_panel.update_current_id)
            self.scraper_worker.data_received.connect(self.result_panel.update_data)
            self.scraper_worker.error_occurred.connect(self.handle_error)
            self.scraper_worker.finished.connect(self.scraping_finished)
            self.scraper_worker.captcha_required.connect(self.handle_captcha_required)
            
            # 开始执行
            self.start_time = datetime.now()
            self.progress_panel.update_stats({'start_time': self.start_time.strftime("%H:%M:%S")})
            self.log_panel.add_log("开始抓取任务", "INFO")
            self.scraper_worker.start()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动失败: {str(e)}")
            self.control_panel.reset_buttons()
    
    def stop_scraping(self):
        """停止抓取"""
        if self.scraper_worker and self.scraper_worker.isRunning():
            self.scraper_worker.stop()
            self.log_panel.add_log("正在停止抓取...", "WARNING")
    
    def handle_error(self, error_msg: str):
        """处理错误"""
        self.log_panel.add_log(error_msg, "ERROR")
        QMessageBox.critical(self, "错误", error_msg)
    
    def handle_captcha_required(self, scene: str):
        """处理滑块验证需求"""
        dialog = CaptchaDialog(scene, self)
        result = dialog.exec()
        
        if result == dialog.DialogCode.Accepted:
            success = not dialog.user_cancelled
        else:
            success = False
        
        # 通知工作线程验证结果
        if self.scraper_worker:
            self.scraper_worker.on_captcha_completed(not success)
    
    def scraping_finished(self):
        """抓取完成"""
        self.control_panel.reset_buttons()
        self.progress_panel.update_status("任务完成")
        
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            self.log_panel.add_log(f"抓取完成，总耗时: {elapsed}", "SUCCESS")


def main():
    """主程序入口"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("抖音数据抓取器")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("YourCompany")
    
    # 设置字体
    font = QFont("Microsoft YaHei UI", 9)
    app.setFont(font)
    
    # 创建主窗口
    window = DouyinScraperMainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()